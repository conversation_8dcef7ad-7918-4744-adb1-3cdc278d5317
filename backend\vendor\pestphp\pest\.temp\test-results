{"version": "pest_3.8.1", "defects": {"Tests\\Feature\\CategoryApiTest::test_can_get_all_categories": 7, "Tests\\Feature\\ProductApiTest::test_can_get_all_products": 7, "Tests\\Feature\\ProductApiTest::test_can_delete_product": 7, "Tests\\Feature\\UserApiTest::test_user_can_update_password": 8, "Tests\\Feature\\ShiftApiTest::test_can_create_shift": 7, "Tests\\Feature\\ShiftApiTest::test_can_update_shift": 7, "Tests\\Feature\\ContractApiTest::test_can_end_contract": 7, "Tests\\Feature\\ContractApiTest::test_can_renew_contract": 7, "Tests\\Unit\\ValueObjects\\EmployeeCertificateTest::test_certificate_expiry_logic": 7, "Tests\\Feature\\ContractEmployeeCertificateTest::test_can_create_contract_with_employee_certificate": 8, "Tests\\Feature\\ContractEmployeeCertificateTest::test_can_create_contract_without_employee_certificate": 8, "Tests\\Feature\\ContractEmployeeCertificateTest::test_contract_certificate_helper_methods": 8, "Tests\\Feature\\ContractEmployeeCertificateTest::test_contract_scopes_for_certificate_status": 8}, "times": {"Tests\\Feature\\CategoryApiTest::test_can_get_all_categories": 0.127, "Tests\\Feature\\CategoryApiTest::test_can_create_category": 0.029, "Tests\\Feature\\CategoryApiTest::test_can_update_category": 0.016, "Tests\\Feature\\CategoryApiTest::test_can_delete_category": 0.015, "Tests\\Feature\\ProductApiTest::test_can_get_all_products": 0.022, "Tests\\Feature\\ProductApiTest::test_can_create_product": 0.022, "Tests\\Feature\\ProductApiTest::test_can_update_product": 0.015, "Tests\\Feature\\ProductApiTest::test_can_delete_product": 0.012, "Tests\\Feature\\ProductApiTest::test_can_search_products": 0.014, "P\\Tests\\Unit\\ExampleTest::__pest_evaluable_that_true_is_true": 0.018, "P\\Tests\\Feature\\ExampleTest::__pest_evaluable_it_returns_a_successful_response": 0.032, "Tests\\Feature\\ProductApiTest::test_can_update_stocks": 0.021, "Tests\\Feature\\UserApiTest::test_user_can_update_their_profile": 10.724, "Tests\\Feature\\UserApiTest::test_user_can_update_password": 0.825, "Tests\\Feature\\ShiftApiTest::test_can_create_shift": 0.195, "Tests\\Feature\\ShiftApiTest::test_can_get_shifts": 0.028, "Tests\\Feature\\ShiftApiTest::test_can_get_single_shift": 0.013, "Tests\\Feature\\ShiftApiTest::test_can_update_shift": 0.018, "Tests\\Feature\\ShiftApiTest::test_can_delete_shift": 0.017, "Tests\\Feature\\ShiftApiTest::test_validation_errors_on_create_shift": 0.021, "Tests\\Feature\\ShiftApiTest::test_end_time_must_be_after_start_time": 0.012, "Tests\\Feature\\ContractApiTest::test_can_create_contract": 0.026, "Tests\\Feature\\ContractApiTest::test_can_get_contracts": 0.023, "Tests\\Feature\\ContractApiTest::test_can_get_single_contract": 0.018, "Tests\\Feature\\ContractApiTest::test_can_update_contract": 0.024, "Tests\\Feature\\ContractApiTest::test_can_end_contract": 0.03, "Tests\\Feature\\ContractApiTest::test_can_renew_contract": 0.023, "Tests\\Feature\\ContractApiTest::test_can_delete_contract": 0.013, "Tests\\Feature\\ContractApiTest::test_validation_errors_on_create_contract": 0.012, "Tests\\Feature\\ContractApiTest::test_end_date_must_be_after_start_date": 0.013, "Tests\\Feature\\ContractApiTest::test_can_get_active_contracts": 0.024, "Tests\\Feature\\ContractApiTest::test_can_get_expired_contracts": 0.023, "Tests\\Unit\\ValueObjects\\EmployeeCertificateTest::test_can_create_employee_certificate": 0.039, "Tests\\Unit\\ValueObjects\\EmployeeCertificateTest::test_can_convert_to_array": 0, "Tests\\Unit\\ValueObjects\\EmployeeCertificateTest::test_can_create_from_array": 0, "Tests\\Unit\\ValueObjects\\EmployeeCertificateTest::test_can_convert_to_json_and_back": 0.001, "Tests\\Unit\\ValueObjects\\EmployeeCertificateTest::test_certificate_expiry_logic": 0.092, "Tests\\Unit\\ValueObjects\\EmployeeCertificateTest::test_validation_throws_exception_for_empty_certificate_number": 0.001, "Tests\\Unit\\ValueObjects\\EmployeeCertificateTest::test_validation_throws_exception_for_empty_institute_name": 0, "Tests\\Unit\\ValueObjects\\EmployeeCertificateTest::test_validation_throws_exception_for_future_date": 0, "Tests\\Unit\\ValueObjects\\EmployeeCertificateTest::test_string_representation": 0, "Tests\\Feature\\ContractEmployeeCertificateTest::test_can_create_contract_with_employee_certificate": 0.317, "Tests\\Feature\\ContractEmployeeCertificateTest::test_can_create_contract_without_employee_certificate": 0.036, "Tests\\Feature\\ContractEmployeeCertificateTest::test_contract_certificate_helper_methods": 0.235, "Tests\\Feature\\ContractEmployeeCertificateTest::test_contract_scopes_for_certificate_status": 0.011}}