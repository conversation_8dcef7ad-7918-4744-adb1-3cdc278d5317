# Employee Certificate Value Object

The Employee Certificate value object has been added to the Contract model to store certificate information for employees.

## Structure

The `EmployeeCertificate` value object contains:
- `certificate_number`: The unique certificate number
- `date`: The date when the certificate was issued
- `institute_name`: The name of the institute that issued the certificate

## Features

- **Automatic expiry calculation**: Certificates are considered valid for 5 years from the issue date
- **JSON storage**: The certificate is stored as JSON in the database
- **Type safety**: Uses a proper value object with validation
- **Helper methods**: Provides convenient methods for checking expiry status

## API Usage

### Creating a Contract with Employee Certificate

```json
POST /api/contracts
{
    "userId": "uuid-here",
    "startDate": "2025-01-01",
    "endDate": "2025-12-31",
    "monthlySalary": 5000,
    "shiftId": "shift-uuid-here",
    "employeeCertificate": {
        "certificateNumber": "CERT-12345",
        "date": "2020-01-15",
        "instituteName": "Medical Institute of Excellence"
    }
}
```

### API Response

```json
{
    "data": {
        "id": "contract-uuid",
        "userId": "user-uuid",
        "startDate": "2025-01-01",
        "endDate": "2025-12-31",
        "monthlySalary": 5000,
        "shiftId": "shift-uuid",
        "pharmacyId": null,
        "employeeCertificate": {
            "certificateNumber": "CERT-12345",
            "date": "2020-01-15",
            "instituteName": "Medical Institute of Excellence",
            "isExpired": false,
            "expiryDate": "2025-01-15",
            "daysUntilExpiry": 198
        },
        "createdAt": "2025-07-01T06:18:02.000000Z",
        "updatedAt": "2025-07-01T06:18:02.000000Z",
        "isActive": false,
        "isExpired": false,
        "isFuture": true,
        "durationInDays": 365,
        "durationInMonths": 11,
        "remainingDays": 365
    }
}
```

## Model Usage

### Creating a Contract with Certificate

```php
use App\Models\Contract;

$contract = Contract::create([
    'user_id' => $userId,
    'start_date' => '2025-01-01',
    'end_date' => '2025-12-31',
    'monthly_salary' => 5000,
    'shift_id' => $shiftId,
    'employee_certificate' => [
        'certificate_number' => 'CERT-12345',
        'date' => '2020-01-15',
        'institute_name' => 'Medical Institute of Excellence',
    ],
]);
```

### Accessing Certificate Information

```php
// Check if contract has a certificate
if ($contract->hasEmployeeCertificate()) {
    $certificate = $contract->employee_certificate;
    
    // Get certificate details
    $number = $certificate->getCertificateNumber();
    $date = $certificate->getDate();
    $institute = $certificate->getInstituteName();
    
    // Check expiry status
    $isExpired = $certificate->isExpired();
    $expiryDate = $certificate->getExpiryDate();
    $daysUntilExpiry = $certificate->getDaysUntilExpiry();
}

// Using contract helper methods
$isExpired = $contract->isCertificateExpired();
$expiryDate = $contract->getCertificateExpiryDate();
$daysUntilExpiry = $contract->getDaysUntilCertificateExpiry();
```

### Query Scopes

```php
// Get contracts with valid certificates
$validContracts = Contract::withValidCertificate()->get();

// Get contracts with expired certificates
$expiredContracts = Contract::withExpiredCertificate()->get();
```

## Validation Rules

The following validation rules are applied:

- `employeeCertificate`: Optional array
- `employeeCertificate.certificateNumber`: Required if certificate provided, string, max 255 characters
- `employeeCertificate.date`: Required if certificate provided, valid date format (Y-m-d), cannot be in the future
- `employeeCertificate.instituteName`: Required if certificate provided, string, max 255 characters

## Database Schema

The certificate is stored in the `employee_certificate` column as JSON:

```sql
ALTER TABLE contracts ADD COLUMN employee_certificate JSON NULL;
```

## Value Object Methods

### EmployeeCertificate Methods

- `getCertificateNumber()`: Returns the certificate number
- `getDate()`: Returns the issue date as Carbon instance
- `getInstituteName()`: Returns the institute name
- `isExpired()`: Returns true if certificate is expired (older than 5 years)
- `getExpiryDate()`: Returns the expiry date (issue date + 5 years)
- `getDaysUntilExpiry()`: Returns days until expiry (0 if already expired)
- `toArray()`: Converts to array format
- `toJson()`: Converts to JSON string
- `equals(EmployeeCertificate $other)`: Compares two certificates
- `__toString()`: String representation of the certificate

### Contract Helper Methods

- `hasEmployeeCertificate()`: Returns true if contract has a certificate
- `isCertificateExpired()`: Returns true if the certificate is expired
- `getCertificateExpiryDate()`: Returns the certificate expiry date or null
- `getDaysUntilCertificateExpiry()`: Returns days until certificate expiry
