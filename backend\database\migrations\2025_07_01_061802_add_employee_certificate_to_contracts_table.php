<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('contracts', function (Blueprint $table) {
            $table->string('certificate_number')->nullable()->after('pharmacy_id');
            $table->date('certificate_issue_date')->nullable()->after('certificate_number');
            $table->string('certificate_institute_name')->nullable()->after('certificate_issue_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('contracts', function (Blueprint $table) {
            $table->dropColumn(['certificate_number', 'certificate_issue_date', 'certificate_institute_name']);
        });
    }
};
