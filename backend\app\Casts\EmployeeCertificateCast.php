<?php

namespace App\Casts;

use App\ValueObjects\EmployeeCertificate;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;
use InvalidArgumentException;

class EmployeeCertificateCast implements CastsAttributes
{
    /**
     * Cast the given value.
     *
     * @param  array<string, mixed>  $attributes
     */
    public function get(Model $model, string $key, mixed $value, array $attributes): ?EmployeeCertificate
    {
        // Check if all certificate fields are present
        if (empty($attributes['certificate_number']) ||
            empty($attributes['certificate_issue_date']) ||
            empty($attributes['certificate_institute_name'])) {
            return null;
        }

        try {
            return new EmployeeCertificate(
                $attributes['certificate_number'],
                \Carbon\Carbon::parse($attributes['certificate_issue_date']),
                $attributes['certificate_institute_name']
            );
        } catch (InvalidArgumentException $e) {
            // Log the error or handle it as needed
            return null;
        }
    }

    /**
     * Prepare the given value for storage.
     *
     * @param  array<string, mixed>  $attributes
     */
    public function set(Model $model, string $key, mixed $value, array $attributes): array
    {
        if ($value === null) {
            return [
                'certificate_number' => null,
                'certificate_issue_date' => null,
                'certificate_institute_name' => null,
            ];
        }

        if ($value instanceof EmployeeCertificate) {
            return [
                'certificate_number' => $value->getCertificateNumber(),
                'certificate_issue_date' => $value->getDate()->toDateString(),
                'certificate_institute_name' => $value->getInstituteName(),
            ];
        }

        if (is_array($value)) {
            $certificate = EmployeeCertificate::fromArray($value);
            return [
                'certificate_number' => $certificate->getCertificateNumber(),
                'certificate_issue_date' => $certificate->getDate()->toDateString(),
                'certificate_institute_name' => $certificate->getInstituteName(),
            ];
        }

        throw new InvalidArgumentException('Value must be an EmployeeCertificate instance or array');
    }
}
