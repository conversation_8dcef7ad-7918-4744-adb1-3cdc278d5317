<?php

namespace App\Casts;

use App\ValueObjects\EmployeeCertificate;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;
use InvalidArgumentException;

class EmployeeCertificateCast implements CastsAttributes
{
    /**
     * Cast the given value.
     *
     * @param  array<string, mixed>  $attributes
     */
    public function get(Model $model, string $key, mixed $value, array $attributes): ?EmployeeCertificate
    {
        if ($value === null) {
            return null;
        }

        try {
            return EmployeeCertificate::fromJson($value);
        } catch (InvalidArgumentException $e) {
            // Log the error or handle it as needed
            return null;
        }
    }

    /**
     * Prepare the given value for storage.
     *
     * @param  array<string, mixed>  $attributes
     */
    public function set(Model $model, string $key, mixed $value, array $attributes): ?string
    {
        if ($value === null) {
            return null;
        }

        if ($value instanceof EmployeeCertificate) {
            return $value->to<PERSON><PERSON>();
        }

        if (is_array($value)) {
            return EmployeeCertificate::fromArray($value)->to<PERSON><PERSON>();
        }

        throw new InvalidArgumentException('Value must be an EmployeeCertificate instance or array');
    }
}
