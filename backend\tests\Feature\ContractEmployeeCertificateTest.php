<?php

namespace Tests\Feature;

use App\Models\Contract;
use App\Models\Shift;
use App\Models\User;
use App\ValueObjects\EmployeeCertificate;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ContractEmployeeCertificateTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_create_contract_with_employee_certificate()
    {
        $user = User::factory()->create();
        $shift = Shift::factory()->create();

        $certificateData = [
            'certificate_number' => 'CERT-12345',
            'date' => '2020-01-15',
            'institute_name' => 'Medical Institute of Excellence',
        ];

        $contract = Contract::create([
            'user_id' => $user->id,
            'start_date' => '2025-01-01',
            'end_date' => '2025-12-31',
            'monthly_salary' => 5000,
            'shift_id' => $shift->id,
            'employee_certificate' => $certificateData,
        ]);

        $this->assertInstanceOf(EmployeeCertificate::class, $contract->employee_certificate);
        $this->assertEquals('CERT-12345', $contract->employee_certificate->getCertificateNumber());
        $this->assertEquals('2020-01-15', $contract->employee_certificate->getDate()->toDateString());
        $this->assertEquals('Medical Institute of Excellence', $contract->employee_certificate->getInstituteName());
    }

    public function test_can_create_contract_without_employee_certificate()
    {
        $user = User::factory()->create();
        $shift = Shift::factory()->create();

        $contract = Contract::create([
            'user_id' => $user->id,
            'start_date' => '2025-01-01',
            'end_date' => '2025-12-31',
            'monthly_salary' => 5000,
            'shift_id' => $shift->id,
        ]);

        $this->assertNull($contract->employee_certificate);
        $this->assertFalse($contract->hasEmployeeCertificate());
    }

    public function test_contract_certificate_helper_methods()
    {
        $user = User::factory()->create();
        $shift = Shift::factory()->create();

        // Create contract with expired certificate (6 years old)
        $expiredCertificateData = [
            'certificate_number' => 'CERT-OLD',
            'date' => Carbon::now()->subYears(6)->toDateString(),
            'institute_name' => 'Old Institute',
        ];

        $contractWithExpiredCert = Contract::create([
            'user_id' => $user->id,
            'start_date' => '2025-01-01',
            'end_date' => '2025-12-31',
            'monthly_salary' => 5000,
            'shift_id' => $shift->id,
            'employee_certificate' => $expiredCertificateData,
        ]);

        $this->assertTrue($contractWithExpiredCert->hasEmployeeCertificate());
        $this->assertTrue($contractWithExpiredCert->isCertificateExpired());
        $this->assertEquals(0, $contractWithExpiredCert->getDaysUntilCertificateExpiry());

        // Create contract with valid certificate (2 years old)
        $validCertificateData = [
            'certificate_number' => 'CERT-VALID',
            'date' => Carbon::now()->subYears(2)->toDateString(),
            'institute_name' => 'Valid Institute',
        ];

        $contractWithValidCert = Contract::create([
            'user_id' => $user->id,
            'start_date' => '2025-01-01',
            'end_date' => '2025-12-31',
            'monthly_salary' => 5000,
            'shift_id' => $shift->id,
            'employee_certificate' => $validCertificateData,
        ]);

        $this->assertTrue($contractWithValidCert->hasEmployeeCertificate());
        $this->assertFalse($contractWithValidCert->isCertificateExpired());
        $this->assertGreaterThan(0, $contractWithValidCert->getDaysUntilCertificateExpiry());
        $this->assertNotNull($contractWithValidCert->getCertificateExpiryDate());
    }

    public function test_contract_scopes_for_certificate_status()
    {
        $user = User::factory()->create();
        $shift = Shift::factory()->create();

        // Contract with valid certificate
        Contract::create([
            'user_id' => $user->id,
            'start_date' => '2025-01-01',
            'end_date' => '2025-12-31',
            'monthly_salary' => 5000,
            'shift_id' => $shift->id,
            'employee_certificate' => [
                'certificate_number' => 'CERT-VALID',
                'date' => Carbon::now()->subYears(2)->toDateString(),
                'institute_name' => 'Valid Institute',
            ],
        ]);

        // Contract with expired certificate
        Contract::create([
            'user_id' => $user->id,
            'start_date' => '2025-01-01',
            'end_date' => '2025-12-31',
            'monthly_salary' => 5000,
            'shift_id' => $shift->id,
            'employee_certificate' => [
                'certificate_number' => 'CERT-EXPIRED',
                'date' => Carbon::now()->subYears(6)->toDateString(),
                'institute_name' => 'Old Institute',
            ],
        ]);

        // Contract without certificate
        Contract::create([
            'user_id' => $user->id,
            'start_date' => '2025-01-01',
            'end_date' => '2025-12-31',
            'monthly_salary' => 5000,
            'shift_id' => $shift->id,
        ]);

        $this->assertEquals(1, Contract::withValidCertificate()->count());
        $this->assertEquals(1, Contract::withExpiredCertificate()->count());
    }
}
