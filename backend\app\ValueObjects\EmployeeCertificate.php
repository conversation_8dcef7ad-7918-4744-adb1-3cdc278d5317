<?php

namespace App\ValueObjects;

use Carbon\Carbon;
use InvalidArgumentException;

class EmployeeCertificate
{
    public function __construct(
        private readonly string $certificateNumber,
        private readonly Carbon $date,
        private readonly string $instituteName
    ) {
        $this->validate();
    }

    public function getCertificateNumber(): string
    {
        return $this->certificateNumber;
    }

    public function getDate(): Carbon
    {
        return $this->date;
    }

    public function getInstituteName(): string
    {
        return $this->instituteName;
    }

    public function toArray(): array
    {
        return [
            'certificate_number' => $this->certificateNumber,
            'date' => $this->date->toDateString(),
            'institute_name' => $this->instituteName,
        ];
    }

    public function toJson(): string
    {
        return json_encode($this->toArray());
    }

    public static function fromArray(array $data): self
    {
        if (!isset($data['certificate_number'], $data['date'], $data['institute_name'])) {
            throw new InvalidArgumentException('Missing required certificate data');
        }

        return new self(
            $data['certificate_number'],
            Carbon::parse($data['date']),
            $data['institute_name']
        );
    }

    public static function fromJson(string $json): self
    {
        $data = json_decode($json, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new InvalidArgumentException('Invalid JSON provided');
        }

        return self::fromArray($data);
    }

    public function equals(EmployeeCertificate $other): bool
    {
        return $this->certificateNumber === $other->certificateNumber
            && $this->date->equalTo($other->date)
            && $this->instituteName === $other->instituteName;
    }

    public function isExpired(): bool
    {
        // Assuming certificates are valid for 5 years from issue date
        return $this->date->copy()->addYears(5)->isPast();
    }

    public function getExpiryDate(): Carbon
    {
        return $this->date->copy()->addYears(5);
    }

    public function getDaysUntilExpiry(): int
    {
        if ($this->isExpired()) {
            return 0;
        }

        $expiryDate = $this->getExpiryDate();
        return now()->diffInDays($expiryDate);
    }

    private function validate(): void
    {
        if (empty(trim($this->certificateNumber))) {
            throw new InvalidArgumentException('Certificate number cannot be empty');
        }

        if (empty(trim($this->instituteName))) {
            throw new InvalidArgumentException('Institute name cannot be empty');
        }

        if ($this->date->isFuture()) {
            throw new InvalidArgumentException('Certificate date cannot be in the future');
        }
    }

    public function __toString(): string
    {
        return "Certificate #{$this->certificateNumber} issued by {$this->instituteName} on {$this->date->toDateString()}";
    }
}
