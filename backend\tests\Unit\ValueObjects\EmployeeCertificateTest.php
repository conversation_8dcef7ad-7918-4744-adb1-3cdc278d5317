<?php

namespace Tests\Unit\ValueObjects;

use App\ValueObjects\EmployeeCertificate;
use Carbon\Carbon;
use InvalidArgumentException;
use PHPUnit\Framework\TestCase;

class EmployeeCertificateTest extends TestCase
{
    public function test_can_create_employee_certificate()
    {
        $certificate = new EmployeeCertificate(
            'CERT-12345',
            Carbon::parse('2020-01-15'),
            'Medical Institute of Excellence'
        );

        $this->assertEquals('CERT-12345', $certificate->getCertificateNumber());
        $this->assertEquals('2020-01-15', $certificate->getDate()->toDateString());
        $this->assertEquals('Medical Institute of Excellence', $certificate->getInstituteName());
    }

    public function test_can_convert_to_array()
    {
        $certificate = new EmployeeCertificate(
            'CERT-12345',
            Carbon::parse('2020-01-15'),
            'Medical Institute of Excellence'
        );

        $expected = [
            'certificate_number' => 'CERT-12345',
            'date' => '2020-01-15',
            'institute_name' => 'Medical Institute of Excellence',
        ];

        $this->assertEquals($expected, $certificate->toArray());
    }

    public function test_can_create_from_array()
    {
        $data = [
            'certificate_number' => 'CERT-12345',
            'date' => '2020-01-15',
            'institute_name' => 'Medical Institute of Excellence',
        ];

        $certificate = EmployeeCertificate::fromArray($data);

        $this->assertEquals('CERT-12345', $certificate->getCertificateNumber());
        $this->assertEquals('2020-01-15', $certificate->getDate()->toDateString());
        $this->assertEquals('Medical Institute of Excellence', $certificate->getInstituteName());
    }

    public function test_can_convert_to_json_and_back()
    {
        $original = new EmployeeCertificate(
            'CERT-12345',
            Carbon::parse('2020-01-15'),
            'Medical Institute of Excellence'
        );

        $json = $original->toJson();
        $restored = EmployeeCertificate::fromJson($json);

        $this->assertTrue($original->equals($restored));
    }

    public function test_certificate_expiry_logic()
    {
        // Certificate from 6 years ago should be expired
        $expiredCertificate = new EmployeeCertificate(
            'CERT-OLD',
            Carbon::now()->subYears(6),
            'Old Institute'
        );

        $this->assertTrue($expiredCertificate->isExpired());
        $this->assertEquals(0, $expiredCertificate->getDaysUntilExpiry());

        // Certificate from 2 years ago should not be expired
        $validCertificate = new EmployeeCertificate(
            'CERT-VALID',
            Carbon::now()->subYears(2),
            'Valid Institute'
        );

        $this->assertFalse($validCertificate->isExpired());
        $this->assertGreaterThan(0, $validCertificate->getDaysUntilExpiry());
    }

    public function test_validation_throws_exception_for_empty_certificate_number()
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Certificate number cannot be empty');

        new EmployeeCertificate(
            '',
            Carbon::parse('2020-01-15'),
            'Medical Institute'
        );
    }

    public function test_validation_throws_exception_for_empty_institute_name()
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Institute name cannot be empty');

        new EmployeeCertificate(
            'CERT-12345',
            Carbon::parse('2020-01-15'),
            ''
        );
    }

    public function test_validation_throws_exception_for_future_date()
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Certificate date cannot be in the future');

        new EmployeeCertificate(
            'CERT-12345',
            Carbon::tomorrow(),
            'Medical Institute'
        );
    }

    public function test_string_representation()
    {
        $certificate = new EmployeeCertificate(
            'CERT-12345',
            Carbon::parse('2020-01-15'),
            'Medical Institute of Excellence'
        );

        $expected = 'Certificate #CERT-12345 issued by Medical Institute of Excellence on 2020-01-15';
        $this->assertEquals($expected, (string) $certificate);
    }
}
